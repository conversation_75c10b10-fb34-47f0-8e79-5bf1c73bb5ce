package com.faw.work.ais.aic.controller;

import com.faw.work.ais.aic.model.domain.RagCategoryPO;
import com.faw.work.ais.aic.model.request.RagCategoryAddRequest;
import com.faw.work.ais.aic.model.request.RagCategoryUpdateRequest;
import com.faw.work.ais.aic.model.response.RagCategoryTreeResponse;
import com.faw.work.ais.aic.service.RagCategoryService;
import com.faw.work.ais.common.Response;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 文档类目表 控制器
 *
 * <AUTHOR>
 */
@Tag(name = "文档类目管理", description = "RAG文档类目管理相关接口")
@RestController
@Slf4j
@RequestMapping("/rag-category")
public class RagCategoryController {

    @Autowired
    private RagCategoryService ragCategoryService;

    @Operation(summary = "新增类目", description = "[author:10200571]")
    @PostMapping("/add")
    public Response<RagCategoryPO> addCategory(@Valid @RequestBody RagCategoryAddRequest request) {
        log.info("接收到新增类目请求: {}", request);
        RagCategoryPO category = ragCategoryService.addCategory(request);
        return Response.success(category);
    }

    @Operation(summary = "删除类目", description = "[author:10200571]")
    @PostMapping("/delete/{categoryId}")
    public Response<Boolean> deleteCategory(@PathVariable Long categoryId) {
        log.info("接收到删除类目请求: categoryId={}", categoryId);
        boolean result = ragCategoryService.deleteCategory(categoryId);
        return Response.success(result);
    }

    @Operation(summary = "修改类目", description = "[author:10200571]")
    @PostMapping("/update")
    public Response<RagCategoryPO> updateCategory(@Valid @RequestBody RagCategoryUpdateRequest request) {
        log.info("接收到修改类目请求: {}", request);
        RagCategoryPO category = ragCategoryService.updateCategory(request);
        return Response.success(category);
    }

    @Operation(summary = "获取类目树结构", description = "[author:10200571]")
    @GetMapping("/tree")
    public Response<List<RagCategoryTreeResponse>> getCategoryTree() {
        List<RagCategoryTreeResponse> tree = ragCategoryService.getCategoryTree();
        return Response.success(tree);
    }
}
