package com.faw.work.ais.aic.model.response;

import com.faw.work.ais.aic.model.domain.RagDocumentPO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 类目树响应DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "类目树响应")
public class RagCategoryTreeResponse {

    @Schema(description = "类目ID")
    private Long id;

    @Schema(description = "类目名称")
    private String name;

    @Schema(description = "租户ID，用于多租户隔离")
    private Long tenantId;

    @Schema(description = "父类目ID，用于构建类目树结构")
    private Long parentId;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新人")
    private String updatedBy;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Schema(description = "子类目列表")
    private List<RagCategoryTreeResponse> children;

    @Schema(description = "该类目下的文档列表")
    private List<RagDocumentPO> documents;
}
